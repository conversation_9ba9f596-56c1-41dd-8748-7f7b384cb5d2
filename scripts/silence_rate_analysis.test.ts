import * as fs from 'fs'
import * as path from 'path'
import { PrismaMongoClient } from '../packages/model/mongodb/prisma'
import { DataService } from '../apps/yuhe/helper/getter/get_data'
import { DateHelper } from '../packages/lib/date/date'

interface SilenceAnalysisResult {
  courseNo: number
  totalStudents: number
  silenceRates: {
    day0: number
    day1: number
    day2: number
    day3: number
    day4: number
    afterCompletion: number
  }
  detailedData: {
    day: number
    totalStudents: number
    silentStudents: number
    silenceRate: number
    silentStudentsList: Array<{
      chatId: string
      studentName: string
      lastMessageTime: Date | null
      silenceDuration: number // 小时
    }>
  }[]
}

describe('沉默率分析', () => {
  jest.setTimeout(60000000)

  test('获取指定期数的沉默率数据', async () => {
    // 指定要分析的期数
    const courseNumbers = [
      250711, 250712, 250714, 250715, 250720,
      250721, 250723, 250727, 250729, 250804, 250808
    ]

    const mongoClient = PrismaMongoClient.getInstance()
    const results: SilenceAnalysisResult[] = []

    for (const courseNo of courseNumbers) {
      console.log(`正在分析期数: ${courseNo}`)

      try {
        // 获取该期数的所有学员
        const students = await getStudentsByCourseNo(mongoClient, courseNo)
        console.log(`期数 ${courseNo} 共有 ${students.length} 名学员`)

        if (students.length === 0) {
          console.log(`期数 ${courseNo} 没有学员数据，跳过`)
          continue
        }

        // 分析每个阶段的沉默率
        const analysisResult: SilenceAnalysisResult = {
          courseNo,
          totalStudents: students.length,
          silenceRates: {
            day0: 0,
            day1: 0,
            day2: 0,
            day3: 0,
            day4: 0,
            afterCompletion: 0
          },
          detailedData: []
        }

        // 分析day0到day4以及完课后的沉默率
        for (let day = 0; day <= 4; day++) {
          const dayAnalysis = await analyzeSilenceRateForDay(mongoClient, students, courseNo, day)
          analysisResult.detailedData.push(dayAnalysis)

          // 更新汇总数据
          const dayKey = `day${day}` as keyof typeof analysisResult.silenceRates
          analysisResult.silenceRates[dayKey] = dayAnalysis.silenceRate
        }

        // 分析完课后的沉默率
        const afterCompletionAnalysis = await analyzeSilenceRateAfterCompletion(mongoClient, students, courseNo)
        analysisResult.detailedData.push(afterCompletionAnalysis)
        analysisResult.silenceRates.afterCompletion = afterCompletionAnalysis.silenceRate

        results.push(analysisResult)

        console.log(`期数 ${courseNo} 分析完成`)
        console.log(`Day0沉默率: ${analysisResult.silenceRates.day0.toFixed(2)}%`)
        console.log(`Day1沉默率: ${analysisResult.silenceRates.day1.toFixed(2)}%`)
        console.log(`Day2沉默率: ${analysisResult.silenceRates.day2.toFixed(2)}%`)
        console.log(`Day3沉默率: ${analysisResult.silenceRates.day3.toFixed(2)}%`)
        console.log(`Day4沉默率: ${analysisResult.silenceRates.day4.toFixed(2)}%`)
        console.log(`完课后沉默率: ${analysisResult.silenceRates.afterCompletion.toFixed(2)}%`)
        console.log('---')

      } catch (error) {
        console.error(`分析期数 ${courseNo} 时出错:`, error)
      }
    }

    // 保存结果
    await saveResults(results)
    console.log('分析完成，结果已保存到 output 目录')
  })
})

// 获取指定期数的所有学员
async function getStudentsByCourseNo(mongoClient: any, courseNo: number) {
  const students = await mongoClient.chat.findMany({
    where: {
      course_no: courseNo,
      // 过滤掉内部成员和测试账号
      NOT: {
        OR: [
          { id: { contains: 'R' } },
          { id: { contains: 'local' } },
          { contact: { wx_name: { in: ['Horus', 'Dremo', 'Kk', 'Toby', '花花', 'SYQ', 'windowLU'] } } } // 根据实际情况调整
        ]
      }
    },
    select: {
      id: true,
      contact: {
        select: {
          wx_name: true,
          wx_id: true
        }
      },
      course_no: true,
      created_at: true
    }
  })

  return students
}

// 分析指定天数的沉默率
async function analyzeSilenceRateForDay(
  mongoClient: any,
  students: any[],
  courseNo: number,
  day: number
) {
  const silentStudents: Array<{
    chatId: string
    studentName: string
    lastMessageTime: Date | null
    silenceDuration: number
  }> = []

  // 获取课程开始时间
  const courseStartTime = getCourseStartTime(courseNo)

  // 计算当前分析日期的时间范围
  const dayStartTime = new Date(courseStartTime)
  dayStartTime.setDate(dayStartTime.getDate() + day)
  dayStartTime.setHours(0, 0, 0, 0)

  const dayEndTime = new Date(dayStartTime)
  dayEndTime.setDate(dayEndTime.getDate() + 1)

  for (const student of students) {
    try {
      // 获取学员在指定时间范围内的最后一条消息
      const lastMessage = await mongoClient.chat_history.findFirst({
        where: {
          chat_id: student.id,
          role: 'user',
          created_at: {
            gte: dayStartTime,
            lt: dayEndTime
          }
        },
        orderBy: {
          created_at: 'desc'
        }
      })

      // 如果没有消息，或者最后一条消息距离当天结束超过24小时，则认为是沉默客户
      let isSilent = false
      let silenceDuration = 0

      if (!lastMessage) {
        // 没有消息，检查是否有AI发送的消息
        const lastAIMessage = await mongoClient.chat_history.findFirst({
          where: {
            chat_id: student.id,
            role: 'assistant',
            created_at: {
              gte: dayStartTime,
              lt: dayEndTime
            }
          },
          orderBy: {
            created_at: 'desc'
          }
        })

        if (lastAIMessage) {
          const timeSinceLastAI = (dayEndTime.getTime() - lastAIMessage.created_at.getTime()) / (1000 * 60 * 60)
          if (timeSinceLastAI >= 24) {
            isSilent = true
            silenceDuration = timeSinceLastAI
          }
        }
      } else {
        // 有用户消息，检查是否超过24小时没有回复
        const timeSinceLastMessage = (dayEndTime.getTime() - lastMessage.created_at.getTime()) / (1000 * 60 * 60)
        if (timeSinceLastMessage >= 24) {
          isSilent = true
          silenceDuration = timeSinceLastMessage
        }
      }

      if (isSilent) {
        silentStudents.push({
          chatId: student.id,
          studentName: student.contact.wx_name,
          lastMessageTime: lastMessage?.created_at || null,
          silenceDuration
        })
      }
    } catch (error) {
      console.error(`分析学员 ${student.id} 在 day${day} 时出错:`, error)
    }
  }

  const silenceRate = (silentStudents.length / students.length) * 100

  return {
    day,
    totalStudents: students.length,
    silentStudents: silentStudents.length,
    silenceRate,
    silentStudentsList: silentStudents
  }
}

// 分析完课后的沉默率
async function analyzeSilenceRateAfterCompletion(
  mongoClient: any,
  students: any[],
  courseNo: number
) {
  const silentStudents: Array<{
    chatId: string
    studentName: string
    lastMessageTime: Date | null
    silenceDuration: number
  }> = []

  // 课程结束时间（day4结束后）
  const courseStartTime = getCourseStartTime(courseNo)
  const courseEndTime = new Date(courseStartTime)
  courseEndTime.setDate(courseEndTime.getDate() + 5) // day4结束

  for (const student of students) {
    try {
      // 获取学员完课后的最后一条消息
      const lastMessage = await mongoClient.chat_history.findFirst({
        where: {
          chat_id: student.id,
          role: 'user',
          created_at: {
            gte: courseEndTime
          }
        },
        orderBy: {
          created_at: 'desc'
        }
      })

      // 检查是否沉默超过24小时
      let isSilent = false
      let silenceDuration = 0

      if (!lastMessage) {
        // 检查完课后是否有AI消息
        const lastAIMessage = await mongoClient.chat_history.findFirst({
          where: {
            chat_id: student.id,
            role: 'assistant',
            created_at: {
              gte: courseEndTime
            }
          },
          orderBy: {
            created_at: 'desc'
          }
        })

        if (lastAIMessage) {
          const timeSinceLastAI = (new Date().getTime() - lastAIMessage.created_at.getTime()) / (1000 * 60 * 60)
          if (timeSinceLastAI >= 24) {
            isSilent = true
            silenceDuration = timeSinceLastAI
          }
        } else {
          // 完课后既没有用户消息也没有AI消息，认为是沉默
          const timeSinceCourseEnd = (new Date().getTime() - courseEndTime.getTime()) / (1000 * 60 * 60)
          if (timeSinceCourseEnd >= 24) {
            isSilent = true
            silenceDuration = timeSinceCourseEnd
          }
        }
      } else {
        const timeSinceLastMessage = (new Date().getTime() - lastMessage.created_at.getTime()) / (1000 * 60 * 60)
        if (timeSinceLastMessage >= 24) {
          isSilent = true
          silenceDuration = timeSinceLastMessage
        }
      }

      if (isSilent) {
        silentStudents.push({
          chatId: student.id,
          studentName: student.contact.wx_name,
          lastMessageTime: lastMessage?.created_at || null,
          silenceDuration
        })
      }
    } catch (error) {
      console.error(`分析学员 ${student.id} 完课后沉默率时出错:`, error)
    }
  }

  const silenceRate = (silentStudents.length / students.length) * 100

  return {
    day: 5, // 用5表示完课后
    totalStudents: students.length,
    silentStudents: silentStudents.length,
    silenceRate,
    silentStudentsList: silentStudents
  }
}

// 根据期数获取课程开始时间
function getCourseStartTime(courseNo: number): Date {
  // 将期数转换为日期格式 (例如: 250711 -> 2025-07-11)
  const courseNoStr = courseNo.toString()
  const year = parseInt(`20${  courseNoStr.substring(0, 2)}`, 10)
  const month = parseInt(courseNoStr.substring(2, 4), 10) - 1 // JavaScript月份从0开始
  const day = parseInt(courseNoStr.substring(4, 6), 10)

  return new Date(year, month, day)
}

// 保存分析结果
async function saveResults(results: SilenceAnalysisResult[]) {
  const outputDir = path.join(__dirname, 'output')
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true })
  }

  // 保存详细的JSON结果
  fs.writeFileSync(
    path.join(outputDir, 'silence_rate_analysis.json'),
    JSON.stringify(results, null, 2)
  )

  // 生成CSV汇总报告
  generateSummaryCSV(results, outputDir)

  // 生成详细的CSV报告
  generateDetailedCSV(results, outputDir)
}

// 生成汇总CSV报告
function generateSummaryCSV(results: SilenceAnalysisResult[], outputDir: string) {
  let csv = '期数,总学员数,Day0沉默率(%),Day1沉默率(%),Day2沉默率(%),Day3沉默率(%),Day4沉默率(%),完课后沉默率(%)\n'

  for (const result of results) {
    csv += `${result.courseNo},${result.totalStudents},${result.silenceRates.day0.toFixed(2)},${result.silenceRates.day1.toFixed(2)},${result.silenceRates.day2.toFixed(2)},${result.silenceRates.day3.toFixed(2)},${result.silenceRates.day4.toFixed(2)},${result.silenceRates.afterCompletion.toFixed(2)}\n`
  }

  fs.writeFileSync(path.join(outputDir, 'silence_rate_summary.csv'), csv)
}

// 生成详细CSV报告
function generateDetailedCSV(results: SilenceAnalysisResult[], outputDir: string) {
  let csv = '期数,阶段,总学员数,沉默学员数,沉默率(%),学员姓名,聊天ID,最后消息时间,沉默时长(小时)\n'

  for (const result of results) {
    for (const dayData of result.detailedData) {
      const stageName = dayData.day === 5 ? '完课后' : `Day${dayData.day}`

      if (dayData.silentStudentsList.length === 0) {
        csv += `${result.courseNo},${stageName},${dayData.totalStudents},${dayData.silentStudents},${dayData.silenceRate.toFixed(2)},,,,\n`
      } else {
        for (const student of dayData.silentStudentsList) {
          const lastMessageTime = student.lastMessageTime ?
            DateHelper.formatDate(student.lastMessageTime, 'YYYY-MM-DD HH:mm:ss') : '无消息'

          csv += `${result.courseNo},${stageName},${dayData.totalStudents},${dayData.silentStudents},${dayData.silenceRate.toFixed(2)},"${student.studentName}",${student.chatId},${lastMessageTime},${student.silenceDuration.toFixed(1)}\n`
        }
      }
    }
  }

  fs.writeFileSync(path.join(outputDir, 'silence_rate_detailed.csv'), csv)
}
