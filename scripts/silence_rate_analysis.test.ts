import * as fs from 'fs'
import * as path from 'path'
import { PrismaMongoClient } from '../packages/model/mongodb/prisma'
import { DataService } from '../apps/yuhe/helper/getter/get_data'
import { DateHelper } from '../packages/lib/date/date'

interface SilenceAnalysisResult {
  courseNo: number
  totalStudents: number
  silenceRates: {
    day0: number
    day1: number
    day2: number
    day3: number
    day4: number
    afterCompletion: number
  }
  detailedData: {
    day: number
    totalStudents: number
    silentStudents: number
    silenceRate: number
    silentStudentsList: Array<{
      chatId: string
      studentName: string
      lastMessageTime: Date | null
      silenceDuration: number // 小时
    }>
  }[]
}

describe('沉默率分析', () => {
  jest.setTimeout(60000000)

  test('获取指定期数的沉默率数据', async () => {
    // 指定要分析的期数 - 转换为8位数字格式
    const courseNumbers = [
      20250711, 20250712, 20250714, 20250715, 20250720,
      20250721, 20250723, 20250727, 20250729, 20250804, 20250808
    ]

    // 先查看数据库中有哪些期数
    const mongoClient = PrismaMongoClient.getInstance()
    console.log('正在查询数据库中的期数...')

    const existingCourses = await mongoClient.chat.findMany({
      select: {
        course_no: true
      },
      distinct: ['course_no'],
      orderBy: {
        course_no: 'desc'
      },
      take: 20
    })

    console.log('数据库中最近的20个期数:', existingCourses.map((c) => c.course_no).filter(Boolean))

    console.log(`🚀 开始并行分析 ${courseNumbers.length} 个期数...`)

    // 并行处理所有期数
    const analysisPromises = courseNumbers.map(async (courseNo) => {
      console.log(`📋 启动分析期数: ${courseNo}`)

      try {
        // 获取该期数的所有学员
        const students = await getStudentsByCourseNo(mongoClient, courseNo)
        console.log(`📊 期数 ${courseNo}: 共有 ${students.length} 名学员`)

        if (students.length === 0) {
          console.log(`⚠️  期数 ${courseNo}: 没有学员数据，跳过`)
          return null
        }

        // 分析每个阶段的沉默率
        const analysisResult: SilenceAnalysisResult = {
          courseNo,
          totalStudents: students.length,
          silenceRates: {
            day0: 0,
            day1: 0,
            day2: 0,
            day3: 0,
            day4: 0,
            afterCompletion: 0
          },
          detailedData: []
        }

        console.log(`🔍 期数 ${courseNo}: 开始分析各阶段沉默率...`)

        // 并行分析day0到day4以及完课后的沉默率
        const dayAnalysisPromises = []

        // Day0-Day4 分析
        for (let day = 0; day <= 4; day++) {
          dayAnalysisPromises.push(
            analyzeSilenceRateForDay(mongoClient, students, courseNo, day)
              .then((result) => {
                console.log(`📈 期数 ${courseNo} Day${day}: 沉默率 ${result.silenceRate.toFixed(1)}%`)
                return result
              })
          )
        }

        // 完课后分析
        dayAnalysisPromises.push(
          analyzeSilenceRateAfterCompletion(mongoClient, students, courseNo)
            .then((result) => {
              console.log(`📈 期数 ${courseNo} 完课后: 沉默率 ${result.silenceRate.toFixed(1)}%`)
              return result
            })
        )

        // 等待所有阶段分析完成
        const dayResults = await Promise.all(dayAnalysisPromises)

        // 整理结果
        for (let i = 0; i <= 4; i++) {
          analysisResult.detailedData.push(dayResults[i])
          const dayKey = `day${i}` as keyof typeof analysisResult.silenceRates
          analysisResult.silenceRates[dayKey] = dayResults[i].silenceRate
        }

        // 完课后结果
        analysisResult.detailedData.push(dayResults[5])
        analysisResult.silenceRates.afterCompletion = dayResults[5].silenceRate

        console.log(`✅ 期数 ${courseNo} 分析完成! 沉默率汇总: Day0(${analysisResult.silenceRates.day0.toFixed(1)}%) Day1(${analysisResult.silenceRates.day1.toFixed(1)}%) Day2(${analysisResult.silenceRates.day2.toFixed(1)}%) Day3(${analysisResult.silenceRates.day3.toFixed(1)}%) Day4(${analysisResult.silenceRates.day4.toFixed(1)}%) 完课后(${analysisResult.silenceRates.afterCompletion.toFixed(1)}%)`)

        return analysisResult

      } catch (error) {
        console.error(`❌ 期数 ${courseNo} 分析失败:`, error.message)
        return null
      }
    })

    // 等待所有期数分析完成
    console.log('⏳ 等待所有期数分析完成...')
    const analysisResults = await Promise.all(analysisPromises)

    // 过滤掉失败的结果
    const results = analysisResults.filter((result) => result !== null) as SilenceAnalysisResult[]

    console.log(`🎉 所有期数分析完成! 成功分析了 ${results.length}/${courseNumbers.length} 个期数`)

    // 保存结果
    await saveResults(results)
    console.log('分析完成，结果已保存到 output 目录')
  })
})

// 获取指定期数的所有学员
async function getStudentsByCourseNo(mongoClient: any, courseNo: number) {
  // 先获取所有该期数的学员
  const allStudents = await mongoClient.chat.findMany({
    where: {
      course_no: courseNo
    },
    select: {
      id: true,
      contact: true,
      course_no: true,
      created_at: true
    }
  })

  // 过滤掉内部成员和测试账号
  const internalMembers = ['Horus', 'Dremo', 'Kk', 'Toby', '花花', 'SYQ', 'windowLU']
  const students = allStudents.filter((student) => {
    // 过滤掉包含'R'或'local'的ID
    if (student.id.includes('R') || student.id.includes('local')) {
      return false
    }

    // 过滤掉内部成员
    if (student.contact && internalMembers.includes(student.contact.wx_name)) {
      return false
    }

    return true
  })

  return students
}

// 分析指定天数的沉默率
async function analyzeSilenceRateForDay(
  mongoClient: any,
  students: any[],
  courseNo: number,
  day: number
) {
  const silentStudents: Array<{
    chatId: string
    studentName: string
    lastMessageTime: Date | null
    silenceDuration: number
  }> = []

  // 获取课程开始时间
  const courseStartTime = getCourseStartTime(courseNo)

  // 计算当前分析日期的时间范围
  const dayStartTime = new Date(courseStartTime)
  dayStartTime.setDate(dayStartTime.getDate() + day)
  dayStartTime.setHours(0, 0, 0, 0)

  const dayEndTime = new Date(dayStartTime)
  dayEndTime.setDate(dayEndTime.getDate() + 1)

  // 批量处理学员，每50个学员输出一次进度
  const batchSize = 50
  for (let i = 0; i < students.length; i += batchSize) {
    const batch = students.slice(i, Math.min(i + batchSize, students.length))

    if (students.length > 100) {
      console.log(`  📊 期数 ${courseNo} Day${day}: 处理学员 ${i + 1}-${Math.min(i + batchSize, students.length)}/${students.length}`)
    }

    for (const student of batch) {
      try {
      // 获取用户在该天的所有消息
        const userMessagesInDay = await mongoClient.chat_history.findMany({
          where: {
            chat_id: student.id,
            role: 'user',
            created_at: {
              gte: dayStartTime,
              lt: dayEndTime
            }
          },
          orderBy: {
            created_at: 'desc'
          }
        })

        let isSilent = false
        let silenceDuration = 0
        let lastMessageTime: Date | null = null

        if (userMessagesInDay.length === 0) {
          // 该天用户没有发消息，算作沉默
          isSilent = true
          silenceDuration = 24 // 整天都沉默
          lastMessageTime = null
        } else {
          // 该天有消息，不算沉默
          isSilent = false
          lastMessageTime = userMessagesInDay[0].created_at // 最后一条消息
        }

        if (isSilent) {
          silentStudents.push({
            chatId: student.id,
            studentName: student.contact.wx_name,
            lastMessageTime: lastMessageTime,
            silenceDuration
          })
        }
      } catch (error) {
        console.error(`分析学员 ${student.id} 在 day${day} 时出错:`, error)
      }
    }
  }

  const silenceRate = (silentStudents.length / students.length) * 100

  return {
    day,
    totalStudents: students.length,
    silentStudents: silentStudents.length,
    silenceRate,
    silentStudentsList: silentStudents
  }
}

// 分析完课后的沉默率
async function analyzeSilenceRateAfterCompletion(
  mongoClient: any,
  students: any[],
  courseNo: number
) {
  const silentStudents: Array<{
    chatId: string
    studentName: string
    lastMessageTime: Date | null
    silenceDuration: number
  }> = []

  // 课程结束时间（day4结束后）
  const courseStartTime = getCourseStartTime(courseNo)
  const courseEndTime = new Date(courseStartTime)
  courseEndTime.setDate(courseEndTime.getDate() + 5) // day4结束

  // 批量处理学员，每50个学员输出一次进度
  const batchSize = 50
  for (let i = 0; i < students.length; i += batchSize) {
    const batch = students.slice(i, Math.min(i + batchSize, students.length))

    if (students.length > 100) {
      console.log(`  📊 期数 ${courseNo} 完课后: 处理学员 ${i + 1}-${Math.min(i + batchSize, students.length)}/${students.length}`)
    }

    for (const student of batch) {
      try {
      // 获取用户完课后的最后一条消息
        const lastUserMessageAfterCourse = await mongoClient.chat_history.findFirst({
          where: {
            chat_id: student.id,
            role: 'user',
            created_at: {
              gte: courseEndTime
            }
          },
          orderBy: {
            created_at: 'desc'
          }
        })

        let isSilent = false
        let silenceDuration = 0
        let lastMessageTime: Date | null = null
        const now = new Date()

        if (!lastUserMessageAfterCourse) {
          // 完课后用户从未发过消息，检查是否超过24小时
          const timeSinceCourseEnd = (now.getTime() - courseEndTime.getTime()) / (1000 * 60 * 60)
          if (timeSinceCourseEnd >= 24) {
            isSilent = true
            silenceDuration = timeSinceCourseEnd
            lastMessageTime = null
          }
        } else {
          // 检查最后一条消息是否超过24小时前
          const timeSinceLastMessage = (now.getTime() - lastUserMessageAfterCourse.created_at.getTime()) / (1000 * 60 * 60)
          lastMessageTime = lastUserMessageAfterCourse.created_at

          if (timeSinceLastMessage >= 24) {
            isSilent = true
            silenceDuration = timeSinceLastMessage
          }
        }

        if (isSilent) {
          silentStudents.push({
            chatId: student.id,
            studentName: student.contact.wx_name,
            lastMessageTime: lastMessageTime,
            silenceDuration
          })
        }
      } catch (error) {
        console.error(`分析学员 ${student.id} 完课后沉默率时出错:`, error)
      }
    }
  }

  const silenceRate = (silentStudents.length / students.length) * 100

  return {
    day: 5, // 用5表示完课后
    totalStudents: students.length,
    silentStudents: silentStudents.length,
    silenceRate,
    silentStudentsList: silentStudents
  }
}

// 根据期数获取课程开始时间
function getCourseStartTime(courseNo: number): Date {
  // 将期数转换为日期格式 (例如: 250711 -> 2025-07-11)
  const courseNoStr = courseNo.toString()
  const year = parseInt(`20${  courseNoStr.substring(0, 2)}`, 10)
  const month = parseInt(courseNoStr.substring(2, 4), 10) - 1 // JavaScript月份从0开始
  const day = parseInt(courseNoStr.substring(4, 6), 10)

  return new Date(year, month, day)
}

// 保存分析结果
async function saveResults(results: SilenceAnalysisResult[]) {
  const outputDir = path.join(__dirname, 'output')
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true })
  }

  // 保存详细的JSON结果
  fs.writeFileSync(
    path.join(outputDir, 'silence_rate_analysis.json'),
    JSON.stringify(results, null, 2)
  )

  // 生成CSV汇总报告
  generateSummaryCSV(results, outputDir)

  // 生成详细的CSV报告
  generateDetailedCSV(results, outputDir)
}

// 生成汇总CSV报告
function generateSummaryCSV(results: SilenceAnalysisResult[], outputDir: string) {
  let csv = '期数,总学员数,Day0沉默率(%),Day1沉默率(%),Day2沉默率(%),Day3沉默率(%),Day4沉默率(%),完课后沉默率(%)\n'

  for (const result of results) {
    csv += `${result.courseNo},${result.totalStudents},${result.silenceRates.day0.toFixed(2)},${result.silenceRates.day1.toFixed(2)},${result.silenceRates.day2.toFixed(2)},${result.silenceRates.day3.toFixed(2)},${result.silenceRates.day4.toFixed(2)},${result.silenceRates.afterCompletion.toFixed(2)}\n`
  }

  fs.writeFileSync(path.join(outputDir, 'silence_rate_summary.csv'), csv)
}

// 生成详细CSV报告
function generateDetailedCSV(results: SilenceAnalysisResult[], outputDir: string) {
  let csv = '期数,阶段,总学员数,沉默学员数,沉默率(%),学员姓名,聊天ID,最后消息时间,沉默时长(小时)\n'

  for (const result of results) {
    for (const dayData of result.detailedData) {
      const stageName = dayData.day === 5 ? '完课后' : `Day${dayData.day}`

      if (dayData.silentStudentsList.length === 0) {
        csv += `${result.courseNo},${stageName},${dayData.totalStudents},${dayData.silentStudents},${dayData.silenceRate.toFixed(2)},,,,\n`
      } else {
        for (const student of dayData.silentStudentsList) {
          const lastMessageTime = student.lastMessageTime ?
            DateHelper.formatDate(student.lastMessageTime, 'YYYY-MM-DD HH:mm:ss') : '无消息'

          csv += `${result.courseNo},${stageName},${dayData.totalStudents},${dayData.silentStudents},${dayData.silenceRate.toFixed(2)},"${student.studentName}",${student.chatId},${lastMessageTime},${student.silenceDuration.toFixed(1)}\n`
        }
      }
    }
  }

  fs.writeFileSync(path.join(outputDir, 'silence_rate_detailed.csv'), csv)
}
