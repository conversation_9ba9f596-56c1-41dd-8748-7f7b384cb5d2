import { PrismaMongoClient } from '../database/prisma'
import * as fs from 'fs'
import * as path from 'path'
import { DataService } from '../helper/getter/get_data'

// 沉默率分析结果类型定义
interface SilenceAnalysisResult {
  courseNo: number
  totalStudents: number
  silenceRates: {
    day0: number
    day1: number
    day2: number
    day3: number
    day4: number
    afterCompletion: number
  }
  detailedData: {
    day: number
    totalStudents: number
    silentStudents: number
    silenceRate: number
    silentStudentsList: Array<{
      chatId: string
      studentName: string
      lastMessageTime: Date | null
      silenceDuration: number // 小时
    }>
  }[]
}

describe('yuhe项目沉默率分析', () => {
  jest.setTimeout(60000000)

  test('获取指定期数的沉默率数据', async () => {
    // 指定要分析的期数
    const courseNumbers = [
      20250711, 20250712, 20250714, 20250715, 20250720, 
      20250721, 20250723, 20250727, 20250729, 20250804, 20250808
    ]

    // 先查看数据库中有哪些期数
    const mongoClient = PrismaMongoClient.getInstance()
    console.log('正在查询yuhe数据库中的期数...')
    
    const existingCourses = await mongoClient.chat.findMany({
      select: {
        course_no: true
      },
      distinct: ['course_no'],
      orderBy: {
        course_no: 'desc'
      },
      take: 20
    })
    
    console.log('yuhe数据库中最近的20个期数:', existingCourses.map(c => c.course_no).filter(Boolean))
    
    console.log(`🚀 开始并行分析 ${courseNumbers.length} 个期数...`)
    
    // 并行处理所有期数
    const analysisPromises = courseNumbers.map(async (courseNo) => {
      console.log(`📋 启动分析期数: ${courseNo}`)
      
      try {
        // 获取该期数的所有学员
        const students = await getStudentsByCourseNo(mongoClient, courseNo)
        console.log(`📊 期数 ${courseNo}: 共有 ${students.length} 名学员`)

        if (students.length === 0) {
          console.log(`⚠️  期数 ${courseNo}: 没有学员数据，跳过`)
          return null
        }

        // 分析每个阶段的沉默率
        const analysisResult: SilenceAnalysisResult = {
          courseNo,
          totalStudents: students.length,
          silenceRates: {
            day0: 0,
            day1: 0,
            day2: 0,
            day3: 0,
            day4: 0,
            afterCompletion: 0
          },
          detailedData: []
        }

        console.log(`🔍 期数 ${courseNo}: 开始分析各阶段沉默率...`)

        // 并行分析day0到day4以及完课后的沉默率
        const dayAnalysisPromises = []
        
        // Day0-Day4 分析
        for (let day = 0; day <= 4; day++) {
          dayAnalysisPromises.push(
            analyzeSilenceRateForDay(mongoClient, students, courseNo, day)
              .then(result => {
                console.log(`📈 期数 ${courseNo} Day${day}: 沉默率 ${result.silenceRate.toFixed(1)}%`)
                return result
              })
          )
        }
        
        // 完课后分析
        dayAnalysisPromises.push(
          analyzeSilenceRateAfterCompletion(mongoClient, students, courseNo)
            .then(result => {
              console.log(`📈 期数 ${courseNo} 完课后: 沉默率 ${result.silenceRate.toFixed(1)}%`)
              return result
            })
        )

        // 等待所有阶段分析完成
        const dayResults = await Promise.all(dayAnalysisPromises)
        
        // 整理结果
        for (let i = 0; i <= 4; i++) {
          analysisResult.detailedData.push(dayResults[i])
          const dayKey = `day${i}` as keyof typeof analysisResult.silenceRates
          analysisResult.silenceRates[dayKey] = dayResults[i].silenceRate
        }
        
        // 完课后结果
        analysisResult.detailedData.push(dayResults[5])
        analysisResult.silenceRates.afterCompletion = dayResults[5].silenceRate

        console.log(`✅ 期数 ${courseNo} 分析完成! 沉默率汇总: Day0(${analysisResult.silenceRates.day0.toFixed(1)}%) Day1(${analysisResult.silenceRates.day1.toFixed(1)}%) Day2(${analysisResult.silenceRates.day2.toFixed(1)}%) Day3(${analysisResult.silenceRates.day3.toFixed(1)}%) Day4(${analysisResult.silenceRates.day4.toFixed(1)}%) 完课后(${analysisResult.silenceRates.afterCompletion.toFixed(1)}%)`)
        
        return analysisResult

      } catch (error) {
        console.error(`❌ 期数 ${courseNo} 分析失败:`, error.message)
        return null
      }
    })

    // 等待所有期数分析完成
    console.log(`⏳ 等待所有期数分析完成...`)
    const analysisResults = await Promise.all(analysisPromises)
    
    // 过滤掉失败的结果
    const results = analysisResults.filter(result => result !== null) as SilenceAnalysisResult[]
    
    console.log(`🎉 所有期数分析完成! 成功分析了 ${results.length}/${courseNumbers.length} 个期数`)

    // 保存结果
    await saveResults(results)
    console.log('分析完成，结果已保存到 output 目录')
  })
})

// 获取指定期数的所有学员
async function getStudentsByCourseNo(mongoClient: any, courseNo: number) {
  // 使用yuhe项目的DataService来获取学员，这样会自动过滤内部成员
  const students = await DataService.getChatsByCourseNo(courseNo)
  return students
}

// 分析指定天数的沉默率
async function analyzeSilenceRateForDay(
  mongoClient: any, 
  students: any[], 
  courseNo: number, 
  day: number
) {
  const silentStudents: Array<{
    chatId: string
    studentName: string
    lastMessageTime: Date | null
    silenceDuration: number
  }> = []

  // 获取课程开始时间
  const courseStartTime = DataService.getCourseStartTime(courseNo)
  
  // 计算当前分析日期的时间范围
  const dayStartTime = new Date(courseStartTime)
  dayStartTime.setDate(dayStartTime.getDate() + day)
  dayStartTime.setHours(0, 0, 0, 0)
  
  const dayEndTime = new Date(dayStartTime)
  dayEndTime.setDate(dayEndTime.getDate() + 1)

  // 批量处理学员，每50个学员输出一次进度
  const batchSize = 50
  for (let i = 0; i < students.length; i += batchSize) {
    const batch = students.slice(i, Math.min(i + batchSize, students.length))
    
    if (students.length > 100) {
      console.log(`  📊 期数 ${courseNo} Day${day}: 处理学员 ${i + 1}-${Math.min(i + batchSize, students.length)}/${students.length}`)
    }

    for (const student of batch) {
      try {
        // 获取用户在该天的所有消息
        const userMessagesInDay = await mongoClient.chat_history.findMany({
          where: {
            chat_id: student.id,
            role: 'user',
            created_at: {
              gte: dayStartTime,
              lt: dayEndTime
            }
          },
          orderBy: {
            created_at: 'desc'
          }
        })

        let isSilent = false
        let silenceDuration = 0
        let lastMessageTime: Date | null = null

        if (userMessagesInDay.length === 0) {
          // 该天用户没有发消息，算作沉默
          isSilent = true
          silenceDuration = 24 // 整天都沉默
          lastMessageTime = null
        } else {
          // 该天有消息，不算沉默
          isSilent = false
          lastMessageTime = userMessagesInDay[0].created_at // 最后一条消息
        }

        if (isSilent) {
          silentStudents.push({
            chatId: student.id,
            studentName: student.contact.wx_name,
            lastMessageTime: lastMessageTime,
            silenceDuration
          })
        }
      } catch (error) {
        console.error(`分析学员 ${student.id} 在 day${day} 时出错:`, error)
      }
    }
  }

  const silenceRate = students.length > 0 ? (silentStudents.length / students.length) * 100 : 0

  return {
    day: day,
    totalStudents: students.length,
    silentStudents: silentStudents.length,
    silenceRate: silenceRate,
    silentStudentsList: silentStudents
  }
}

// 分析完课后的沉默率
async function analyzeSilenceRateAfterCompletion(
  mongoClient: any,
  students: any[],
  courseNo: number
) {
  const silentStudents: Array<{
    chatId: string
    studentName: string
    lastMessageTime: Date | null
    silenceDuration: number
  }> = []

  // Day4课程结束时间（晚上22:00）
  const courseStartTime = DataService.getCourseStartTime(courseNo)
  const day4CourseEndTime = new Date(courseStartTime)
  day4CourseEndTime.setDate(day4CourseEndTime.getDate() + 3) // day4是第4天，从day1开始算起
  day4CourseEndTime.setHours(22, 0, 0, 0) // 设置为晚上22:00

  // 批量处理学员，每50个学员输出一次进度
  const batchSize = 50
  for (let i = 0; i < students.length; i += batchSize) {
    const batch = students.slice(i, Math.min(i + batchSize, students.length))
    
    if (students.length > 100) {
      console.log(`  📊 期数 ${courseNo} 完课后: 处理学员 ${i + 1}-${Math.min(i + batchSize, students.length)}/${students.length}`)
    }

    for (const student of batch) {
      try {
        // 获取用户在Day4课程结束后的最后一条消息
        const lastUserMessageAfterCourse = await mongoClient.chat_history.findFirst({
          where: {
            chat_id: student.id,
            role: 'user',
            created_at: {
              gte: day4CourseEndTime
            }
          },
          orderBy: {
            created_at: 'desc'
          }
        })

        let isSilent = false
        let silenceDuration = 0
        let lastMessageTime: Date | null = null
        const now = new Date()

        // 计算从Day4课程结束到现在的时间
        const timeSinceDay4End = (now.getTime() - day4CourseEndTime.getTime()) / (1000 * 60 * 60)

        if (!lastUserMessageAfterCourse) {
          // Day4课程结束后用户从未发过消息
          if (timeSinceDay4End >= 24) {
            isSilent = true
            silenceDuration = timeSinceDay4End
            lastMessageTime = null
          }
        } else {
          // 检查最后一条消息距离现在是否超过24小时
          const timeSinceLastMessage = (now.getTime() - lastUserMessageAfterCourse.created_at.getTime()) / (1000 * 60 * 60)
          lastMessageTime = lastUserMessageAfterCourse.created_at

          if (timeSinceLastMessage >= 24) {
            isSilent = true
            silenceDuration = timeSinceLastMessage
          }
        }

        if (isSilent) {
          silentStudents.push({
            chatId: student.id,
            studentName: student.contact.wx_name,
            lastMessageTime: lastMessageTime,
            silenceDuration
          })
        }
      } catch (error) {
        console.error(`分析学员 ${student.id} 完课后沉默率时出错:`, error)
      }
    }
  }

  const silenceRate = students.length > 0 ? (silentStudents.length / students.length) * 100 : 0

  return {
    day: -1, // 完课后用-1表示
    totalStudents: students.length,
    silentStudents: silentStudents.length,
    silenceRate: silenceRate,
    silentStudentsList: silentStudents
  }
}

// 保存分析结果
async function saveResults(results: SilenceAnalysisResult[]) {
  const outputDir = path.join(__dirname, 'output')
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true })
  }

  // 保存JSON格式的完整结果
  fs.writeFileSync(
    path.join(outputDir, 'yuhe_silence_rate_analysis.json'),
    JSON.stringify(results, null, 2)
  )

  // 生成汇总CSV报告
  let summaryCsv = 'CourseNo,TotalStudents,Day0SilenceRate,Day1SilenceRate,Day2SilenceRate,Day3SilenceRate,Day4SilenceRate,AfterCompletionSilenceRate\n'
  
  for (const result of results) {
    summaryCsv += `${result.courseNo},${result.totalStudents},${result.silenceRates.day0.toFixed(2)}%,${result.silenceRates.day1.toFixed(2)}%,${result.silenceRates.day2.toFixed(2)}%,${result.silenceRates.day3.toFixed(2)}%,${result.silenceRates.day4.toFixed(2)}%,${result.silenceRates.afterCompletion.toFixed(2)}%\n`
  }
  
  fs.writeFileSync(path.join(outputDir, 'yuhe_silence_rate_summary.csv'), summaryCsv)

  // 生成详细CSV报告（包含沉默客户信息）
  let detailedCsv = 'CourseNo,Day,ChatId,StudentName,LastMessageTime,SilenceDuration(Hours)\n'
  
  for (const result of results) {
    for (const dayData of result.detailedData) {
      const dayLabel = dayData.day === -1 ? 'AfterCompletion' : `Day${dayData.day}`
      
      for (const silentStudent of dayData.silentStudentsList) {
        const lastMsgTime = silentStudent.lastMessageTime ? silentStudent.lastMessageTime.toISOString() : 'Never'
        detailedCsv += `${result.courseNo},${dayLabel},${silentStudent.chatId},"${silentStudent.studentName}",${lastMsgTime},${silentStudent.silenceDuration.toFixed(2)}\n`
      }
    }
  }
  
  fs.writeFileSync(path.join(outputDir, 'yuhe_silence_rate_detailed.csv'), detailedCsv)
}
