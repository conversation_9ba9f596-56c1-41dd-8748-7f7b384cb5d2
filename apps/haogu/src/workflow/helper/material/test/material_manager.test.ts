import { MaterialManager } from '../material_manager'
import { commonMessageSender } from '../../../../config/instance/send_message_instance'
import { Config } from 'config'


describe('materialManagerTest', () => {

  it('sendMaterial', async () => {
    Config.setting.localTest = false
    await commonMessageSender.sendMaterial('242829731708929028_113', { sourceId:'166'  }, { force:true })
  }, 9e8)

})