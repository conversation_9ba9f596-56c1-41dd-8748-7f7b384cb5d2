'use server'

import { AdminPrismaMongoClient } from '@/lib/prisma'
import { MaterialManager } from 'haogu/src/workflow/helper/material/material_manager'
import { ContentItem } from 'model/haogu/crm/type'

export interface Material {
  id: string
  source_id: string
  type: number
  title: string
  description: string
  data: any
  created_at?: Date
}

export interface MaterialQueryParams {
  page?: number
  pageSize?: number
  type?: number
}

export async function queryMaterials(params: MaterialQueryParams = {}) {
  const { page = 1, pageSize = 20, type } = params
  const skip = (page - 1) * pageSize

  try {
    const whereCondition: any = {}
    if (type !== undefined) {
      whereCondition.type = type
    }

    const [materials, total] = await Promise.all([
      AdminPrismaMongoClient.getHaoguInstance().material.findMany({
        where: whereCondition,
        skip,
        take: pageSize,
        orderBy: {
          id: 'desc'  // 使用id排序代替created_at
        }
      }),
      AdminPrismaMongoClient.getHaoguInstance().material.count({
        where: whereCondition
      })
    ])

    return {
      materials,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    }
  } catch (error) {
    console.error('Error querying materials:', error)
    throw error
  }
}

export async function updateMaterial(id: string, updates: { title?: string; description?: string }) {
  try {
    const material = await AdminPrismaMongoClient.getHaoguInstance().material.update({
      where: { id },
      data: updates
    })
    return material
  } catch (error) {
    console.error('Error updating material:', error)
    throw error
  }
}

export async function searchHaoGuMaterials(page: number, pageSize: number, type: number, mediaType?: number) {
  try {
    const materialManager = new MaterialManager()
    const result = await materialManager.searchMaterialFromHaoGu(page, pageSize, type, mediaType)
    if (result == null) {
      throw ('result is null')
    }
    return result
  } catch (error) {
    console.error('Error searching HaoGu materials:', error)
    throw error
  }
}

export async function importMaterialsFromHaoGu(contentItems: ContentItem[]) {
  try {
    const materialManager = new MaterialManager()
    const results: Array<{ id: string | number; status: 'imported' | 'already_exists' }> = []

    for (const item of contentItems) {
      const existingMaterial = await materialManager.searchMaterialById(item.id.toString())
      if (!existingMaterial) {
        await materialManager.saveMaterial(item)
        results.push({ id: item.id, status: 'imported' })
      } else {
        results.push({ id: item.id, status: 'already_exists' })
      }
    }

    return results
  } catch (error) {
    console.error('Error importing materials from HaoGu:', error)
    throw error
  }
}

export async function getMaterialTypes() {
  try {
    const materials = await AdminPrismaMongoClient.getHaoguInstance().material.groupBy({
      by: ['type'],
      _count: {
        type: true
      }
    })

    return materials.map((item) => ({
      type: item.type,
      count: item._count.type
    }))
  } catch (error) {
    console.error('Error getting material types:', error)
    throw error
  }
}