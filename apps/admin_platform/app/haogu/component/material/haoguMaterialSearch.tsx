'use client'

import { useEffect, useState } from 'react'
import { searchHaoGuMaterials, importMaterialsFromHaoGu } from '@/app/haogu/api/material'
import { ContentItem, ContentItemMediaType, ContentItemType, ContentListRes } from 'model/haogu/crm/type'
import { toast } from 'react-toastify'

const CONTENT_TYPES = {
  [ContentItemType.Article]: '文章',
  [ContentItemType.File]: '文件',
  [ContentItemType.Link]: '链接',
  [ContentItemType.Poster]: '海报',
  [ContentItemType.Media]: '多媒体',
  [ContentItemType.Channels]: '视频号',
  [ContentItemType.Text]: '文本'
}

const MEDIA_TYPES = {
  [ContentItemMediaType.Image]: '图片',
  [ContentItemMediaType.Audio]: '音频',
  [ContentItemMediaType.Video]: '视频',
  [ContentItemMediaType.Voice]: '语音'
}

export default function HaoGuMaterialSearch() {
  const [materials, setMaterials] = useState<ContentItem[]>([])
  const [loading, setLoading] = useState(false)
  const [importing, setImporting] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [total, setTotal] = useState(0)
  const [selectedType, setSelectedType] = useState<ContentItemType>(ContentItemType.Article)
  const [selectedMediaType, setSelectedMediaType] = useState<ContentItemMediaType | undefined>(undefined)
  const [selectedMaterials, setSelectedMaterials] = useState<Set<string>>(new Set())
  const pageSize = 20

  const loadMaterials = async () => {
    setLoading(true)
    try {
      const result = await searchHaoGuMaterials(currentPage, pageSize, selectedType, selectedMediaType) as ContentListRes
      setMaterials(result.records)
      setTotalPages(result.pages)
      setTotal(result.total)
    } catch (error) {
      toast.error(`搜索素材失败: ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadMaterials()
  }, [currentPage, selectedType, selectedMediaType])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handleTypeChange = (type: ContentItemType) => {
    setSelectedType(type)
    setSelectedMediaType(undefined)
    setCurrentPage(1)
    setSelectedMaterials(new Set())
  }

  const handleMediaTypeChange = (mediaType: ContentItemMediaType | undefined) => {
    setSelectedMediaType(mediaType)
    setCurrentPage(1)
    setSelectedMaterials(new Set())
  }

  const handleSelectMaterial = (materialId: string) => {
    const newSelected = new Set(selectedMaterials)
    if (newSelected.has(materialId)) {
      newSelected.delete(materialId)
    } else {
      newSelected.add(materialId)
    }
    setSelectedMaterials(newSelected)
  }

  const handleSelectAll = () => {
    if (selectedMaterials.size === materials.length) {
      setSelectedMaterials(new Set())
    } else {
      setSelectedMaterials(new Set(materials.map((m) => m.id.toString())))
    }
  }

  const handleImportSelected = async () => {
    if (selectedMaterials.size === 0) {
      alert('请先选择要导入的素材')
      return
    }

    setImporting(true)
    try {
      const selectedItems = materials.filter((m) => selectedMaterials.has(m.id.toString()))
      const results = await importMaterialsFromHaoGu(selectedItems)

      const importedCount = results.filter((r) => r.status === 'imported').length
      const existingCount = results.filter((r) => r.status === 'already_exists').length

      alert(`导入完成！新导入 ${importedCount} 个素材，${existingCount} 个素材已存在`)
      setSelectedMaterials(new Set())
    } catch (error) {
      console.error('导入素材失败:', error)
      alert(`导入素材失败: ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setImporting(false)
    }
  }

  const getPreviewContent = (material: ContentItem) => {
    switch (material.type) {
      case ContentItemType.Media:
        if (material.media_type === ContentItemMediaType.Image && material.img) {
          return (
            <img
              src={material.img}
              alt="预览"
              className="w-16 h-16 object-cover rounded"
            />
          )
        }
        if (material.media_type === ContentItemMediaType.Video && material.video_img) {
          return (
            <div className="relative">
              <img
                src={material.video_img}
                alt="视频封面"
                className="w-16 h-16 object-cover rounded"
              />
              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 rounded">
                <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M8 5v10l8-5-8-5z"/>
                </svg>
              </div>
            </div>
          )
        }
        break
      default:
        return (
          <div className="w-16 h-16 bg-gray-100 rounded flex items-center justify-center">
            <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
        )
    }
    return null
  }

  const getTypeDisplay = (material: ContentItem) => {
    const typeText = CONTENT_TYPES[material.type || ContentItemType.Text] || `类型${material.type}`
    if (material.type === ContentItemType.Media && material.media_type) {
      const mediaText = MEDIA_TYPES[material.media_type] || `媒体${material.media_type}`
      return `${typeText}-${mediaText}`
    }
    return typeText
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">HaoGu素材搜索</h1>

      {/* 类型筛选 */}
      <div className="mb-6">
        <div className="mb-4">
          <label className="label">
            <span className="label-text">素材类型</span>
          </label>
          <div className="flex flex-wrap gap-2">
            {Object.entries(CONTENT_TYPES).map(([typeValue, typeName]) => (
              <button
                key={typeValue}
                className={`btn btn-sm ${selectedType === Number(typeValue) ? 'btn-primary' : 'btn-outline'}`}
                onClick={() => handleTypeChange(Number(typeValue) as ContentItemType)}
              >
                {typeName}
              </button>
            ))}
          </div>
        </div>

        {/* 多媒体子类型筛选 */}
        {selectedType === ContentItemType.Media && (
          <div className="mb-4">
            <label className="label">
              <span className="label-text">媒体类型</span>
            </label>
            <div className="flex flex-wrap gap-2">
              <button
                className={`btn btn-sm ${selectedMediaType === undefined ? 'btn-primary' : 'btn-outline'}`}
                onClick={() => handleMediaTypeChange(undefined)}
              >
                全部
              </button>
              {Object.entries(MEDIA_TYPES).map(([mediaValue, mediaName]) => (
                <button
                  key={mediaValue}
                  className={`btn btn-sm ${selectedMediaType === Number(mediaValue) ? 'btn-primary' : 'btn-outline'}`}
                  onClick={() => handleMediaTypeChange(Number(mediaValue) as ContentItemMediaType)}
                >
                  {mediaName}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* 批量操作 */}
      <div className="mb-4 flex justify-between items-center">
        <div className="flex items-center gap-4">
          <label className="cursor-pointer label">
            <input
              type="checkbox"
              className="checkbox"
              checked={selectedMaterials.size === materials.length && materials.length > 0}
              onChange={handleSelectAll}
            />
            <span className="label-text ml-2">全选</span>
          </label>
          <span className="text-sm text-gray-500">
            已选择 {selectedMaterials.size} 个素材
          </span>
        </div>
        <button
          className="btn btn-primary"
          onClick={handleImportSelected}
          disabled={selectedMaterials.size === 0 || importing}
        >
          {importing ? (
            <>
              <span className="loading loading-spinner loading-sm"></span>
              导入中...
            </>
          ) : (
            `导入选中素材 (${selectedMaterials.size})`
          )}
        </button>
      </div>

      {/* 加载状态 */}
      {loading && (
        <div className="flex justify-center py-8">
          <span className="loading loading-spinner loading-lg"></span>
        </div>
      )}

      {/* 素材列表 */}
      {!loading && (
        <div className="overflow-x-auto">
          <table className="table table-zebra w-full">
            <thead>
              <tr>
                <th>选择</th>
                <th>预览</th>
                <th>标题</th>
                <th>描述</th>
                <th>类型</th>
                <th>ID</th>
              </tr>
            </thead>
            <tbody>
              {materials.map((material) => (
                <tr key={material.id}>
                  <td>
                    <input
                      type="checkbox"
                      className="checkbox"
                      checked={selectedMaterials.has(material.id.toString())}
                      onChange={() => handleSelectMaterial(material.id.toString())}
                    />
                  </td>
                  <td>
                    <div className="w-16 h-16 flex items-center justify-center">
                      {getPreviewContent(material)}
                    </div>
                  </td>
                  <td>
                    <div className="font-medium">
                      {material.title || '无标题'}
                    </div>
                  </td>
                  <td>
                    <div className="max-w-xs truncate">
                      {material.sphfeed_desc || material.content || '无描述'}
                    </div>
                  </td>
                  <td>
                    <span className="badge badge-outline">
                      {getTypeDisplay(material)}
                    </span>
                  </td>
                  <td>
                    <span className="text-sm text-gray-500">
                      {material.id}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {materials.length === 0 && !loading && (
            <div className="text-center py-8 text-gray-500">
              暂无素材数据
            </div>
          )}
        </div>
      )}

      {/* 分页 */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-6">
          <div className="join">
            <button
              className="join-item btn"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              上一页
            </button>

            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const page = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i
              return (
                <button
                  key={page}
                  className={`join-item btn ${currentPage === page ? 'btn-active' : ''}`}
                  onClick={() => handlePageChange(page)}
                >
                  {page}
                </button>
              )
            })}

            <button
              className="join-item btn"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              下一页
            </button>
          </div>
        </div>
      )}

      {/* 分页信息 */}
      {materials.length > 0 && (
        <div className="text-center mt-4 text-sm text-gray-500">
          第 {currentPage} 页，共 {totalPages} 页，总计 {total} 条记录
        </div>
      )}
    </div>
  )
}